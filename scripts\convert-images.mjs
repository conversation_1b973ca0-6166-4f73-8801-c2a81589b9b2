import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

const imageDir = path.join(process.cwd(), 'public/Websites');

async function convertImages() {
  try {
    const files = await fs.readdir(imageDir);
    const imageFiles = files.filter(file =>
      file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.jpeg')
    );

    console.log(`Found ${imageFiles.length} images to convert.`);

    for (const file of imageFiles) {
      const inputPath = path.join(imageDir, file);
      const webpPath = path.join(imageDir, file.replace(/\.(png|jpe?g)$/i, '.webp'));

      // Check if WebP version already exists
      try {
        await fs.access(webpPath);
        console.log(`Skipping ${file}, WebP version already exists.`);
        continue;
      } catch (error) {
        // File doesn't exist, so we can convert it
      }

      console.log(`Converting ${file} to WebP...`);
      await sharp(inputPath)
        .webp({ quality: 80 })
        .toFile(webpPath);
      console.log(`Successfully converted ${file} to ${path.basename(webpPath)}`);
    }

    console.log('Image conversion process completed.');
  } catch (error) {
    console.error('Error during image conversion:', error);
    process.exit(1);
  }
}

convertImages();
