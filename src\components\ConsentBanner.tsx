import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { updateConsent } from '@/utils/consent';

const ConsentBanner: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('cookie_consent');
    if (consent === null) {
      setIsVisible(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookie_consent', 'granted');
    updateConsent({ analytics_storage: 'granted', ad_storage: 'granted' });
    setIsVisible(false);
  };

  const handleDecline = () => {
    localStorage.setItem('cookie_consent', 'denied');
    updateConsent({ analytics_storage: 'denied', ad_storage: 'denied' });
    setIsVisible(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: '100%' }}
          animate={{ y: '0%' }}
          exit={{ y: '100%' }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className="fixed bottom-0 left-0 right-0 z-50 p-4"
        >
          <div className="max-w-4xl mx-auto p-6 rounded-2xl premium-glass shadow-lg border border-white/10">
            <div className="md:flex md:items-center md:justify-between">
              <div className="md:w-2/3">
                <h3 className="text-lg font-semibold text-white">Cookie Consent</h3>
                <p className="text-sm text-foreground/80 mt-2">
                  We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept", you consent to our use of cookies.
                </p>
              </div>
              <div className="mt-4 md:mt-0 md:ml-6 flex-shrink-0 flex gap-4">
                <Button
                  onClick={handleDecline}
                  className="bg-transparent border border-white/20 text-white hover:bg-white/10 transition-colors"
                >
                  Decline
                </Button>
                <Button
                  onClick={handleAccept}
                  className="premium-button"
                >
                  Accept
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ConsentBanner;
