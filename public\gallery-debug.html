<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            border: 1px solid #333;
            padding: 10px;
            border-radius: 8px;
        }
        .test-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            border-radius: 4px;
            font-size: 12px;
        }
        .success { background: #0f5132; color: #d1e7dd; }
        .error { background: #842029; color: #f8d7da; }
        .loading { background: #664d03; color: #fff3cd; }
    </style>
</head>
<body>
    <h1>Gallery Image Debug Test</h1>
    <p>Testing image accessibility for Visual Craftsmanship gallery...</p>
    
    <div id="test-results"></div>
    
    <script>
        const productImages = [
            { id: 1, src: '/product-pictures/1 (1).png', alt: 'Coffee packaging mockup' },
            { id: 2, src: '/product-pictures/1 (2).png', alt: 'Cosmetic bottle product photography' },
            { id: 3, src: '/product-pictures/1 (3).png', alt: 'Supplement packaging visualization' },
            { id: 4, src: '/product-pictures/1 (4).png', alt: 'Skincare product packaging design' },
            { id: 5, src: '/product-pictures/1 (5).png', alt: 'Premium perfume bottle photography' },
            { id: 6, src: '/product-pictures/1 (6).png', alt: 'Health supplement jar design' },
            { id: 7, src: '/product-pictures/1 (7).png', alt: 'Organic tea packaging mockup' },
            { id: 8, src: '/product-pictures/1 (8).png', alt: 'Wellness product photography' }
        ];

        function createTestItem(image) {
            const item = document.createElement('div');
            item.className = 'test-item';
            
            const img = document.createElement('img');
            img.className = 'test-image';
            img.src = image.src;
            img.alt = image.alt;
            
            const status = document.createElement('div');
            status.className = 'status loading';
            status.textContent = 'Loading...';
            
            const info = document.createElement('div');
            info.innerHTML = `
                <strong>ID:</strong> ${image.id}<br>
                <strong>Path:</strong> ${image.src}<br>
                <strong>Encoded:</strong> ${image.src.replace(/ /g, '%20')}
            `;
            
            img.onload = () => {
                status.className = 'status success';
                status.textContent = '✅ Loaded successfully';
                console.log('✅ Image loaded:', image.src);
            };
            
            img.onerror = (e) => {
                status.className = 'status error';
                status.textContent = '❌ Failed to load';
                console.error('❌ Image failed:', image.src, e);
            };
            
            item.appendChild(img);
            item.appendChild(info);
            item.appendChild(status);
            
            return item;
        }

        function runTest() {
            const container = document.getElementById('test-results');
            const grid = document.createElement('div');
            grid.className = 'test-grid';

            productImages.forEach(image => {
                const testItem = createTestItem(image);
                grid.appendChild(testItem);
            });

            container.appendChild(grid);

            // Test direct fetch
            console.log('🔍 Testing direct image access...');
            productImages.forEach(async (image) => {
                try {
                    const response = await fetch(image.src);
                    console.log(`📊 ${image.src}: ${response.status} ${response.statusText}`);
                    if (!response.ok) {
                        console.error(`❌ HTTP Error: ${response.status} for ${image.src}`);
                    }
                } catch (error) {
                    console.error(`📊 ${image.src}: Fetch failed`, error);
                }
            });

            // Test file existence with different approaches
            console.log('🔍 Testing alternative paths...');
            const testPaths = [
                '/product-pictures/1%20(1).png',
                '/product-pictures/1 (1).png',
                '/Product Pictures/1 (1).png',
                '/Product%20Pictures/1%20(1).png'
            ];

            testPaths.forEach(async (path) => {
                try {
                    const response = await fetch(path);
                    console.log(`🧪 Test path ${path}: ${response.status} ${response.statusText}`);
                } catch (error) {
                    console.error(`🧪 Test path ${path}: Failed`, error);
                }
            });
        }

        // Run test when page loads
        document.addEventListener('DOMContentLoaded', runTest);
    </script>
</body>
</html>
