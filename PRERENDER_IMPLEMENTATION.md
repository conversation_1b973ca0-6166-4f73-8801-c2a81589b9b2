# 🤖 Bot Prerender Implementation

## Overview

This implementation provides fully rendered HTML to bots and crawlers while maintaining the fast SPA experience for regular users. When a bot requests any route, it receives prerendered HTML with complete meta tags, structured data, and content.

## How It Works

### 1. Bot Detection
The system detects bots using a comprehensive user-agent pattern in `vercel.json`:
```json
"value": "(?i)(bot|crawl|spider|slurp|facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram|discord|slack|requests|aiohttp|go-http-client|curl|wget|python|ruby|java|php|perl|node)"
```

Additionally, the edge function implements a heuristic approach:
- If User-Agent does **not** contain `Mozilla` **and** `Accept` header includes `text/html`, treat as crawler
- This catches generic HTTP clients like `python-requests`, `aiohttp`, etc.

### 2. Request Routing
- **Bots**: Routed to `/api/prerender?url=/$1` edge function
- **Regular users**: Served the normal SPA via `/index.html`

### 3. Prerendered Content
The edge function generates complete HTML including:
- ✅ Full meta tags (Open Graph, Twitter Cards)
- ✅ Structured data (JSON-LD)
- ✅ Route-specific content and titles
- ✅ SEO-optimized HTML structure
- ✅ Canonical URLs

## Files Modified

### `vercel.json`
Added bot detection rewrite rule before the SPA catch-all:
```json
{
  "source": "/(.*)",
  "has": [
    {
      "type": "header",
      "key": "User-Agent", 
      "value": "(?i)(bot|crawl|spider|slurp|facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram|discord|slack)"
    }
  ],
  "destination": "/api/prerender?url=/$1"
}
```

### `api/prerender.ts`
New Vercel Edge Function that:
- Detects bot user agents
- Generates route-specific HTML content
- Returns fully rendered HTML with proper meta tags
- Includes structured data for SEO
- Handles fallback for errors

### `public/service-worker.js`
Added bypass for prerendered requests:
```javascript
// Skip prerendered requests (for bots/crawlers)
if (event.request.headers.has('X-Prerender')) {
  return;
}
```

## Supported Routes

The prerender function includes optimized content for:
- `/` - Homepage with services overview
- `/services` - Services page
- `/portfolio` - Portfolio showcase
- `/contact` - Contact information

## Bot User Agents Supported

- Google (Googlebot)
- Bing (Bingbot)
- Yahoo (Slurp)
- DuckDuckGo (DuckDuckBot)
- Baidu (Baiduspider)
- Yandex (YandexBot)
- Facebook (FacebookExternalHit)
- Twitter (Twitterbot)
- LinkedIn (LinkedInBot)
- Pinterest (PinterestBot)
- Reddit (RedditBot)
- WhatsApp
- Telegram
- Discord
- Slack
- Skype
- Apple (Applebot)

## Testing

### Test Bot Requests
```bash
# Test with different bot user agents
curl -A "Googlebot" https://econicmedia.pro/
curl -A "Twitterbot" https://econicmedia.pro/
curl -A "FacebookExternalHit" https://econicmedia.pro/
curl -A "LinkedInBot" https://econicmedia.pro/services
curl -A "Slackbot-LinkExpanding 1.0" https://econicmedia.pro/portfolio
```

### Test Regular Browser
```bash
# Should redirect to SPA
curl -A "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" https://econicmedia.pro/
```

### Expected Bot Response
- Status: `200 OK`
- Content-Type: `text/html; charset=utf-8`
- Headers: `X-Prerender: true`
- Body: Full HTML with meta tags and content

## Performance

- **Edge Runtime**: Sub-250ms response times
- **Caching**: 1-hour cache for prerendered content
- **Fallback**: Graceful error handling with basic HTML
- **No Impact**: Zero impact on regular SPA performance

## SEO Benefits

1. **Complete Meta Tags**: Open Graph, Twitter Cards, canonical URLs
2. **Structured Data**: JSON-LD for rich snippets
3. **Route-Specific Content**: Tailored titles and descriptions
4. **Fast Response**: Edge function ensures quick bot responses
5. **Social Sharing**: Proper preview cards for all platforms

## Monitoring

The implementation includes:

- `X-Prerender: true` header for identification
- `X-Served-By: prerender` header for debugging
- `X-Prerender-Error: true` for error tracking
- `X-User-Agent-Debug` header showing the detected User-Agent
- Console logging for debugging (production only)
- Graceful fallback for any errors

## Troubleshooting UA Matching

### Current Detection Patterns

**Vercel.json Regex Pattern:**
```
(?i)(bot|crawl|spider|slurp|facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram|discord|slack|requests|aiohttp|go-http-client|curl|wget|python|ruby|java|php|perl|node)
```

**Known Bot User-Agents (Edge Function):**
- `googlebot`, `bingbot`, `slurp`, `duckduckbot`, `baiduspider`, `yandexbot`
- `facebookexternalhit`, `twitterbot`, `linkedinbot`, `pinterestbot`, `redditbot`
- `whatsapp`, `telegram`, `discord`, `slack`, `skype`, `applebot`
- `python-requests`, `requests`, `aiohttp`, `go-http-client`
- `curl`, `wget`, `python`, `ruby`, `java`, `php`, `perl`, `node`

**Heuristic Detection:**
- User-Agent does **not** contain `Mozilla`
- **AND** `Accept` header includes `text/html`
- **Result:** Treated as crawler/bot

### Debug Headers

Check these headers in responses to verify prerender behavior:

- `X-Served-By: prerender` - Confirms request was handled by prerender function
- `X-User-Agent-Debug` - Shows the User-Agent string that was detected
- `X-Prerender: true` - Indicates prerendered content
- `X-Prerender-Error: true` - Indicates fallback was used due to error

### Common Issues

1. **Known bots getting SPA shell:**
   - Check if User-Agent matches the regex pattern
   - Verify Vercel deployment has latest configuration
   - Check edge function logs for errors

2. **Generic HTTP clients not detected:**
   - Ensure they don't contain `Mozilla` in User-Agent
   - Verify they send `Accept: text/html` header
   - Check if they match any pattern in the expanded regex

3. **Regular browsers getting prerendered content:**
   - Verify User-Agent contains `Mozilla`
   - Check if Accept header is properly set
   - Review heuristic logic for false positives

## Future Enhancements

1. **Dynamic Content**: Fetch real-time data for prerendering
2. **Image Optimization**: Generate optimized social images
3. **A/B Testing**: Different content for different bots
4. **Analytics**: Track bot visits and performance
5. **Cache Warming**: Pregenerate popular routes
