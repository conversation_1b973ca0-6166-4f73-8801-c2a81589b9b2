# Test script for bot prerender functionality
# Tests various bot user agents to verify prerender is working

$baseUrl = "https://econicmedia.pro"
$botUserAgents = @(
    "Googlebot/2.1 (+http://www.google.com/bot.html)",
    "Twitterbot/1.0",
    "facebookexternalhit/1.1",
    "LinkedInBot/1.0",
    "Slackbot-LinkExpanding 1.0",
    "WhatsApp/2.0"
)

$regularUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

Write-Host "🤖 Testing Bot Prerender Functionality" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Test each bot user agent
foreach ($userAgent in $botUserAgents) {
    Write-Host "Testing: $userAgent" -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri $baseUrl -UserAgent $userAgent -UseBasicParsing
        
        $isPrerendered = $response.Headers.ContainsKey('X-Prerender')
        $contentLength = $response.Content.Length
        $hasMetaTags = $response.Content -match '<meta property="og:title"'
        $hasStructuredData = $response.Content -match 'application/ld\+json'
        
        Write-Host "  ✅ Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "  📄 Content Length: $contentLength bytes" -ForegroundColor White
        Write-Host "  🤖 Prerendered: $(if($isPrerendered){'Yes'}else{'No'})" -ForegroundColor $(if($isPrerendered){'Green'}else{'Red'})
        Write-Host "  🏷️  Meta Tags: $(if($hasMetaTags){'Present'}else{'Missing'})" -ForegroundColor $(if($hasMetaTags){'Green'}else{'Red'})
        Write-Host "  📊 Structured Data: $(if($hasStructuredData){'Present'}else{'Missing'})" -ForegroundColor $(if($hasStructuredData){'Green'}else{'Red'})
        
        if ($contentLength -gt 1000) {
            Write-Host "  ✅ Content appears to be fully rendered" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  Content may be incomplete" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "  ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Test regular browser (should get SPA)
Write-Host "Testing Regular Browser (should get SPA):" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -UserAgent $regularUserAgent -UseBasicParsing
    
    $isPrerendered = $response.Headers.ContainsKey('X-Prerender')
    $contentLength = $response.Content.Length
    
    Write-Host "  ✅ Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "  📄 Content Length: $contentLength bytes" -ForegroundColor White
    Write-Host "  🤖 Prerendered: $(if($isPrerendered){'Yes (unexpected)'}else{'No (correct)'})" -ForegroundColor $(if($isPrerendered){'Red'}else{'Green'})
    
    if ($contentLength -lt 5000) {
        Write-Host "  ✅ Appears to be SPA shell (correct)" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  Content seems large for SPA shell" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "  ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Test Complete!" -ForegroundColor Cyan
Write-Host "If bots show Prerendered: Yes and content >1000 bytes, the implementation is working!" -ForegroundColor Green
