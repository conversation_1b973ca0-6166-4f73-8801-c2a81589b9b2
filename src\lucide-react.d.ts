declare module 'lucide-react/dist/esm/icons/x' {
  const X: React.FC<React.SVGProps<SVGSVGElement>>;
  export default X;
}

declare module 'lucide-react/dist/esm/icons/panel-left' {
  const PanelLeft: React.FC<React.SVGProps<SVGSVGElement>>;
  export default PanelLeft;
}

declare module 'lucide-react/dist/esm/icons/check' {
  const Check: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Check;
}

declare module 'lucide-react/dist/esm/icons/chevron-down' {
  const ChevronDown: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ChevronDown;
}

declare module 'lucide-react/dist/esm/icons/chevron-up' {
  const ChevronUp: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ChevronUp;
}

declare module 'lucide-react/dist/esm/icons/grip-vertical' {
  const GripVertical: React.FC<React.SVGProps<SVGSVGElement>>;
  export default GripVertical;
}

declare module 'lucide-react/dist/esm/icons/circle' {
  const Circle: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Circle;
}

declare module 'lucide-react/dist/esm/icons/chevron-left' {
  const ChevronLeft: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ChevronLeft;
}

declare module 'lucide-react/dist/esm/icons/chevron-right' {
  const ChevronRight: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ChevronRight;
}

declare module 'lucide-react/dist/esm/icons/more-horizontal' {
  const MoreHorizontal: React.FC<React.SVGProps<SVGSVGElement>>;
  export default MoreHorizontal;
}

declare module 'lucide-react/dist/esm/icons/dot' {
  const Dot: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Dot;
}

declare module 'lucide-react/dist/esm/icons/search' {
  const Search: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Search;
}

declare module 'lucide-react/dist/esm/icons/arrow-left' {
  const ArrowLeft: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ArrowLeft;
}

declare module 'lucide-react/dist/esm/icons/arrow-right' {
  const ArrowRight: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ArrowRight;
}

declare module 'lucide-react/dist/esm/icons/star' {
  const Star: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Star;
}

declare module 'lucide-react/dist/esm/icons/message-square-quote' {
  const MessageSquareQuote: React.FC<React.SVGProps<SVGSVGElement>>;
  export default MessageSquareQuote;
}

declare module 'lucide-react/dist/esm/icons/dollar-sign' {
  const DollarSign: React.FC<React.SVGProps<SVGSVGElement>>;
  export default DollarSign;
}

declare module 'lucide-react/dist/esm/icons/shield' {
  const Shield: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Shield;
}

declare module 'lucide-react/dist/esm/icons/bar-chart-3' {
  const BarChart3: React.FC<React.SVGProps<SVGSVGElement>>;
  export default BarChart3;
}

declare module 'lucide-react/dist/esm/icons/headphones' {
  const Headphones: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Headphones;
}

declare module 'lucide-react/dist/esm/icons/zap' {
  const Zap: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Zap;
}

declare module 'lucide-react/dist/esm/icons/target' {
  const Target: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Target;
}

declare module 'lucide-react/dist/esm/icons/award' {
  const Award: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Award;
}

declare module 'lucide-react/dist/esm/icons/trending-up' {
  const TrendingUp: React.FC<React.SVGProps<SVGSVGElement>>;
  export default TrendingUp;
}

declare module 'lucide-react/dist/esm/icons/check-circle' {
  const CheckCircle: React.FC<React.SVGProps<SVGSVGElement>>;
  export default CheckCircle;
}

declare module 'lucide-react/dist/esm/icons/sparkles' {
  const Sparkles: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Sparkles;
}

declare module 'lucide-react/dist/esm/icons/crown' {
  const Crown: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Crown;
}

declare module 'lucide-react/dist/esm/icons/gem' {
  const Gem: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Gem;
}

declare module 'lucide-react/dist/esm/icons/medal' {
  const Medal: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Medal;
}

declare module 'lucide-react/dist/esm/icons/line-chart' {
  const LineChart: React.FC<React.SVGProps<SVGSVGElement>>;
  export default LineChart;
}

declare module 'lucide-react/dist/esm/icons/activity' {
  const Activity: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Activity;
}

declare module 'lucide-react/dist/esm/icons/pie-chart' {
  const PieChart: React.FC<React.SVGProps<SVGSVGElement>>;
  export default PieChart;
}

declare module 'lucide-react/dist/esm/icons/building-2' {
  const Building2: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Building2;
}

declare module 'lucide-react/dist/esm/icons/users' {
  const Users: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Users;
}

declare module 'lucide-react/dist/esm/icons/globe' {
  const Globe: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Globe;
}

declare module 'lucide-react/dist/esm/icons/settings' {
  const Settings: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Settings;
}

declare module 'lucide-react/dist/esm/icons/lightbulb' {
  const Lightbulb: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Lightbulb;
}

declare module 'lucide-react/dist/esm/icons/cog' {
  const Cog: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Cog;
}

declare module 'lucide-react/dist/esm/icons/database' {
  const Database: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Database;
}

declare module 'lucide-react/dist/esm/icons/briefcase' {
  const Briefcase: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Briefcase;
}

declare module 'lucide-react/dist/esm/icons/calculator' {
  const Calculator: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Calculator;
}

declare module 'lucide-react/dist/esm/icons/file-text' {
  const FileText: React.FC<React.SVGProps<SVGSVGElement>>;
  export default FileText;
}

declare module 'lucide-react/dist/esm/icons/handshake' {
  const Handshake: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Handshake;
}

declare module 'lucide-react/dist/esm/icons/network' {
  const Network: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Network;
}

declare module 'lucide-react/dist/esm/icons/layers' {
  const Layers: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Layers;
}

declare module 'lucide-react/dist/esm/icons/package' {
  const Package: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Package;
}

declare module 'lucide-react/dist/esm/icons/workflow' {
  const Workflow: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Workflow;
}

declare module 'lucide-react/dist/esm/icons/compass' {
  const Compass: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Compass;
}

declare module 'lucide-react/dist/esm/icons/map' {
  const Map: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Map;
}

declare module 'lucide-react/dist/esm/icons/bookmark' {
  const Bookmark: React.FC<React.SVGProps<SVGSVGElement>>;
  export default Bookmark;
}

declare module 'lucide-react/dist/esm/icons/arrow-up-right' {
  const ArrowUpRight: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ArrowUpRight;
}
