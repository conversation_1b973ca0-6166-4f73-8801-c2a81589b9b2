import React, { Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { Partytown } from '@builder.io/partytown/react';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import ErrorBoundary from '@/components/ErrorBoundary';
import Index from '@/pages/Index';
import PrivacyPolicy from '@/pages/PrivacyPolicy';
import TermsOfService from '@/pages/TermsOfService';
import ConsentBanner from '@/components/ConsentBanner';

// Loading fallback for the entire application
const AppLoader = () => (
  <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-black via-gray-900 to-blue-900">
    <div className="text-center">
      <div className="w-12 h-12 border-2 border-cyan-400/20 border-t-cyan-400 rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-cyan-400 text-lg font-medium">Loading Econic Media...</p>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <ErrorBoundary>
        <Partytown forward={['vercel.*']} />
        <Suspense fallback={<AppLoader />}>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/terms-of-service" element={<TermsOfService />} />
          </Routes>

          {/* Performance monitoring - only in production */}
          {import.meta.env.PROD && (
            <>
              <Analytics />
              <SpeedInsights />
            </>
          )}
        </Suspense>
        <ConsentBanner />
      </ErrorBoundary>
    </Router>
  );
}

export default App;
