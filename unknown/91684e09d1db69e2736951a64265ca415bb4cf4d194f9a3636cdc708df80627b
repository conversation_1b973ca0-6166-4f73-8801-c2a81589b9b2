import React, { memo, Suspense, lazy } from 'react';
import { LucideProps } from 'lucide-react';

// Lazy load icons for better performance
const iconComponents = {
  Palette: lazy(() => import('lucide-react').then(module => ({ default: module.Palette }))),
  Share2: lazy(() => import('lucide-react').then(module => ({ default: module.Share2 }))),
  Rotate3d: lazy(() => import('lucide-react').then(module => ({ default: module.Rotate3d }))),
  Layout: lazy(() => import('lucide-react').then(module => ({ default: module.Layout }))),
  Film: lazy(() => import('lucide-react').then(module => ({ default: module.Film }))),
  Camera: lazy(() => import('lucide-react').then(module => ({ default: module.Camera }))),
  Code: lazy(() => import('lucide-react').then(module => ({ default: module.Code }))),
  ShoppingCart: lazy(() => import('lucide-react').then(module => ({ default: module.ShoppingCart }))),
  Smartphone: lazy(() => import('lucide-react').then(module => ({ default: module.Smartphone }))),
  Database: lazy(() => import('lucide-react').then(module => ({ default: module.Database }))),
  Bot: lazy(() => import('lucide-react').then(module => ({ default: module.Bot }))),
  Wrench: lazy(() => import('lucide-react').then(module => ({ default: module.Wrench }))),
  BarChart3: lazy(() => import('lucide-react').then(module => ({ default: module.BarChart3 }))),
  Settings: lazy(() => import('lucide-react').then(module => ({ default: module.Settings }))),
  Shield: lazy(() => import('lucide-react').then(module => ({ default: module.Shield }))),
};

interface LazyIconProps extends LucideProps {
  name: keyof typeof iconComponents;
}

// Simple loading placeholder for icons
const IconPlaceholder = memo(({ size = 36, className }: { size?: number; className?: string }) => (
  <div 
    className={`animate-pulse bg-gray-600 rounded ${className}`}
    style={{ width: size, height: size }}
  />
));

IconPlaceholder.displayName = 'IconPlaceholder';

const LazyIcon = memo(({ name, ...props }: LazyIconProps) => {
  const IconComponent = iconComponents[name];

  if (!IconComponent) {
    return <IconPlaceholder size={props.size} className={props.className} />;
  }

  return (
    <Suspense fallback={<IconPlaceholder size={props.size} className={props.className} />}>
      <IconComponent {...props} />
    </Suspense>
  );
});

LazyIcon.displayName = 'LazyIcon';

export default LazyIcon;
