import { createRoot } from 'react-dom/client'
import { HelmetProvider } from 'react-helmet-async'
import App from './App.tsx'
import './index.css'

// DEPLOYMENT: Force fresh deployment after maintenance page removal

// Performance-optimized critical styling for 95+ speed score
if (typeof window !== 'undefined') {
  // Deferred service worker registration for better performance
  setTimeout(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/service-worker.js')
        .then(() => {
          if (import.meta.env.PROD) {
            console.log('SW registered');
          }
        })
        .catch(() => {
          // Silent fail for better performance
        });
    }
  }, 2000);
}

// Development debugging scripts removed for cleaner console output

// Ensure DOM is ready before rendering
function initializeApp() {
  try {
    // Minimal console filtering for performance
    if (import.meta.env.DEV) {
      const originalError = console.error;
      const extensionPatterns = ['chrome-extension', 'moz-extension', 'contentscript', 'Extension context'];

      console.error = (...args) => {
        const message = args.join(' ');
        if (!extensionPatterns.some(pattern => message.includes(pattern))) {
          originalError.apply(console, args);
        }
      };
    }

    // Performance-optimized rendering
    document.documentElement.classList.add('no-animations');

    // Enable animations after initial render
    requestAnimationFrame(() => {
      document.documentElement.classList.remove('no-animations');
    });

    // Get root element and ensure it exists
    const rootElement = document.getElementById("root");
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // Render the application
    const root = createRoot(rootElement);
    root.render(
      <HelmetProvider>
        <App />
      </HelmetProvider>
    );

  } catch (error) {
    console.error('Failed to initialize app:', error);
    // Fallback error display
    const rootElement = document.getElementById("root");
    if (rootElement) {
      rootElement.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; font-family: system-ui;">
          <div style="text-align: center; padding: 2rem;">
            <h1 style="color: #ef4444; margin-bottom: 1rem;">Application Error</h1>
            <p style="color: #6b7280; margin-bottom: 1rem;">Failed to load the application. Please refresh the page.</p>
            <button onclick="window.location.reload()" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
              Refresh Page
            </button>
          </div>
        </div>
      `;
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  // DOM is already ready
  initializeApp();
}
