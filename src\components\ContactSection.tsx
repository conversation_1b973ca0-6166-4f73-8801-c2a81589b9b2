import React, { useState } from 'react';
import { useLanguage } from '@/hooks/use-language';
import ScrollReveal from './ScrollReveal';
import { Mail, MessageSquare, Plus, Minus } from 'lucide-react';

// FAQ Component
const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqItems = [
    {
      question: "What is your process for web design and development?",
      answer: "Our web design and development process is collaborative and transparent. We start with a deep dive into your brand and goals, followed by wireframing and design mockups. Once the design is approved, our development team builds a high-performance, SEO-optimized website. We keep you involved at every stage to ensure the final product exceeds your expectations."
    },
    {
      question: "How do you approach SEO for new websites?",
      answer: "We take a comprehensive approach to SEO, starting with in-depth keyword research and competitive analysis. We implement on-page SEO best practices, including meta tags, header tags, and optimized content. We also focus on technical SEO, such as site speed, mobile-friendliness, and structured data, to build a strong foundation for long-term search visibility."
    },
    {
      question: "Do you offer ongoing website maintenance and support?",
      answer: "Yes, we offer a range of website maintenance and support packages to keep your site secure, up-to-date, and performing at its best. Our services include regular backups, security monitoring, software updates, and performance optimization, so you can focus on your business while we take care of your website."
    }
  ];

  return (
    <div className="max-w-4xl mx-auto mt-12 sm:mt-16 md:mt-20 lg:mt-24 px-3 sm:px-4 md:px-6">
      <ScrollReveal>
        <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-black section-title-ultra-bold text-center mb-6 sm:mb-8 md:mb-12 leading-tight">
          <span className="text-gradient-lightning-white drop-shadow-glow">Frequently Asked Questions</span>
        </h3>
      </ScrollReveal>

      <div className="space-y-6">
        {faqItems.map((item, index) => (
          <ScrollReveal key={index} delay={index * 150}>
            <div className="group relative overflow-hidden rounded-2xl luxury-card transition-all duration-500 ease-out hover:border-premium-gold/40 hover:shadow-[0_16px_48px_-8px_rgba(255,215,0,0.3)]">
              {/* Premium glow effects */}
              <div className="absolute -inset-2 rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-10 blur-xl z-0 transition-opacity duration-500"></div>
              <div className="absolute -inset-1 rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-5 blur-lg z-0 transition-opacity duration-500"></div>

              <button
                type="button"
                className="flex items-center justify-between w-full p-3 sm:p-4 md:p-6 text-left font-semibold text-premium-platinum text-sm sm:text-base md:text-lg rounded-t-xl sm:rounded-t-2xl hover:bg-white/5 transition-colors duration-300 relative z-10 min-h-[48px] sm:min-h-[52px] touch-manipulation"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <span className="pr-3 sm:pr-4">{item.question}</span>
                {openIndex === index ?
                  <Minus className="h-5 w-5 sm:h-6 sm:w-6 text-premium-gold flex-shrink-0" /> :
                  <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-premium-gold flex-shrink-0" />
                }
              </button>
              <div
                className={`overflow-hidden transition-all duration-500 ${
                  openIndex === index ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="p-3 sm:p-4 md:p-6 pt-0 text-foreground/90 text-sm sm:text-base md:text-lg leading-relaxed relative z-10">
                  {item.answer}
                </div>
              </div>

              {/* Floating premium accent elements */}
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-premium-gold rounded-full opacity-0 group-hover:opacity-50 z-20 transition-opacity duration-500"></div>
              <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-premium-silver rounded-full opacity-0 group-hover:opacity-30 z-20 transition-opacity duration-500"></div>
            </div>
          </ScrollReveal>
        ))}
      </div>
    </div>
  );
};

const ContactSection: React.FC = () => {
  const { t } = useLanguage();
  
  return (
    <section id="contact" className="section-padding relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Enhanced gradient orb decorations optimized for darker background - Static - Mobile Responsive */}
      <div className="absolute top-20 right-1/4 w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 bg-gradient-ocean opacity-15 sm:opacity-20 rounded-full blur-2xl sm:blur-3xl z-5"></div>
      <div className="absolute bottom-20 left-1/4 w-44 h-44 sm:w-56 sm:h-56 md:w-64 md:h-64 bg-gradient-sunset opacity-12 sm:opacity-18 rounded-full blur-2xl sm:blur-3xl z-5"></div>
      <div className="absolute top-1/2 left-1/2 w-48 h-48 sm:w-60 sm:h-60 md:w-72 md:h-72 bg-gradient-aurora opacity-8 sm:opacity-12 rounded-full blur-2xl sm:blur-3xl z-5"></div>



      <div className="section-container">
        <div className="section-header">
          <ScrollReveal>
            <h2 className="section-title-enhanced text-gradient-lightning-white drop-shadow-glow">
              {t('contact.title')}
            </h2>
          </ScrollReveal>

          <ScrollReveal delay={300}>
            <p className="section-description">
              {t('contact.subtitle')}
            </p>
          </ScrollReveal>
        </div>
        
        {/* Premium CTA Buttons */}
        <div className="max-w-2xl mx-auto">
          <ScrollReveal delay={400}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-8">
              {/* Premium Email Button */}
              <a
                href="mailto:<EMAIL>"
                className="group relative overflow-hidden rounded-xl px-3 sm:px-4 md:px-8 py-3 sm:py-4 md:py-6 premium-button text-black font-semibold text-sm sm:text-base md:text-lg transition-all duration-300 ease-out hover:scale-105 active:scale-95 flex items-center justify-center gap-2 sm:gap-3 md:gap-4 min-h-[48px] touch-manipulation"
              >
                <Mail className="h-5 w-5 sm:h-5 sm:w-5 md:h-6 md:w-6 transition-transform duration-300 group-hover:scale-110" />
                <span>Drop us an email</span>
              </a>

              {/* Premium WhatsApp Button */}
              <a
                href="https://wa.me/491723773552"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative overflow-hidden rounded-xl px-3 sm:px-4 md:px-8 py-3 sm:py-4 md:py-6 luxury-card border-2 border-neon-cyan/40 hover:border-neon-cyan/60 hover:shadow-[0_0_40px_rgba(0,229,229,0.4)] text-neon-cyan font-semibold text-sm sm:text-base md:text-lg transition-all duration-500 ease-out hover:scale-105 flex items-center justify-center gap-2 sm:gap-3 md:gap-4 min-h-[48px] touch-manipulation"
              >
                <div className="absolute -inset-1 rounded-xl bg-gradient-neon opacity-0 group-hover:opacity-10 blur-lg z-0 transition-opacity duration-500"></div>
                <MessageSquare className="h-5 w-5 sm:h-5 sm:w-5 md:h-6 md:w-6 transition-transform duration-300 group-hover:scale-110 relative z-10" />
                <span className="relative z-10">Talk to us on WhatsApp</span>
              </a>
            </div>
          </ScrollReveal>
        </div>
        
        {/* FAQ Section */}
        <FAQ />
      </div>
    </section>
  );
};

export default ContactSection;
