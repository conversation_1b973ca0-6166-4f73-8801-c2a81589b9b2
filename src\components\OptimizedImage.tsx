 import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  placeholder?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  onLoad?: () => void;
  onError?: () => void;
}

// Custom hook for intersection observer
function useIntersectionObserver(ref: React.RefObject<Element>) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [ref]);

  return isVisible;
}

// Generate srcset for responsive images
// function generateSrcSet(src: string): string {
//   if (src.startsWith('http') || src.includes('data:')) {
//     return '';
//   }

//   try {
//     const url = new URL(src, window.location.origin);
//     const pathname = url.pathname;
//     const ext = pathname.split('.').pop()?.toLowerCase();

//     if (!ext || !['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
//       return '';
//     }

//     const baseName = pathname.replace(`.${ext}`, '');

//     return [
//       `${baseName}-400w.${ext} 400w`,
//       `${baseName}-800w.${ext} 800w`,
//       `${baseName}-1200w.${ext} 1200w`,
//       `${baseName}-1600w.${ext} 1600w`
//     ].join(', ');
//   } catch {
//     return '';
//   }
// }

/**
 * Optimized Image component with lazy loading, WebP support, and performance optimizations
 * Improves LCP and reduces CLS by providing proper dimensions and loading states
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  containerClassName = '',
  width,
  height,
  priority = false,
  loading = 'lazy',
  placeholder,
  objectFit = 'cover',
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(containerRef);

  // Handle URL encoding for special characters while preserving proper path structure
  // For static assets with spaces, encode only the spaces to match browser expectations
  const encodedSrc = src.replace(/ /g, '%20');

  // Generate WebP source with proper URL encoding for spaces
  const webpSrc = encodedSrc.replace(/\.(png|jpe?g)$/i, '.webp');

  // Set proper loading attribute based on priority
  const loadingAttr = priority ? 'eager' : loading;

  // Generate fetchpriority attribute for LCP images
  const fetchPriority = priority ? 'high' : 'auto';

  // Set decoding attribute based on priority
  const decodingAttr = priority ? 'sync' : 'async';

  // Generate srcset and only include it if it's not empty
  // const srcSet = generateSrcSet(src);
  const srcSetAttr = {}; // srcSet ? { srcSet } : {};

  // Calculate aspect ratio for CLS prevention
  const aspectRatio = width && height ? (height / width) * 100 : undefined;

  useEffect(() => {
    console.log('🔄 OptimizedImage: Component effect triggered for:', encodedSrc);
    if (priority && imageRef.current) {
      console.log('⚡ OptimizedImage: Preloading priority image:', encodedSrc);
      // If image is prioritized, preload it using the encoded source
      const img = new Image();
      img.src = encodedSrc;
      img.onload = () => {
        console.log('✅ OptimizedImage: Priority preload successful:', encodedSrc);
        setIsLoaded(true);
        setHasError(false);
        onLoad?.();
      };
      img.onerror = () => {
        console.error('❌ OptimizedImage: Priority preload failed:', encodedSrc);
        setHasError(true);
      };
    }
  }, [priority, encodedSrc, onLoad]);

  // Handle image load event
  const handleImageLoad = () => {
    console.log('✅ OptimizedImage: Successfully loaded:', encodedSrc);
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  // Handle image error event with detailed logging
  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = event.target as HTMLImageElement;
    console.error('❌ OptimizedImage: Failed to load image:', {
      originalSrc: src,
      encodedSrc: encodedSrc,
      actualSrc: target.src,
      priority: priority,
      error: event
    });
    setHasError(true);
    setIsLoaded(false);
    onError?.();
  };

  // Determine object-fit class based on the prop
  const objectFitClass = objectFit === 'cover' ? 'object-cover' :
                        objectFit === 'contain' ? 'object-contain' :
                        objectFit === 'fill' ? 'object-fill' :
                        objectFit === 'none' ? 'object-none' :
                        'object-scale-down';

  // Removed debug logging for better performance

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${containerClassName}`}
      style={{ aspectRatio: width && height ? `${width} / ${height}` : undefined }}
    >
      {/* Temporarily force all images to be visible for debugging */}
      {true ? (
        <>
          {/* Error fallback */}
          {hasError ? (
            <div className="flex items-center justify-center bg-gray-800/50 text-gray-400 text-sm p-4 rounded min-h-[200px]">
              <div className="text-center">
                <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
                <span>Image failed to load</span>
              </div>
            </div>
          ) : (
            <>
              {/* Low quality placeholder styling */}
              {!isLoaded && (
                <div
                  className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
                  data-placeholder-color={placeholder}
                >
                  <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                </div>
              )}
              {/* Simplified image loading without WebP for debugging */}
              <img
                ref={imageRef}
                src={encodedSrc}
                alt={alt}
                width={width}
                height={height}
                loading={loadingAttr}
                decoding={decodingAttr}
                className={`
                  transition-opacity duration-300 ${objectFitClass} ${className}
                  ${isLoaded ? 'opacity-100' : 'opacity-0'}
                  ${aspectRatio ? 'absolute inset-0 w-full h-full' : ''}
                `}
                onLoad={handleImageLoad}
                onError={handleImageError}
                data-fetchpriority={fetchPriority}
                {...srcSetAttr}
              />
            </>
          )}
        </>
      ) : (
        // Empty placeholder when not visible and not priority
        <div style={{ paddingTop: aspectRatio ? `${aspectRatio}%` : '56.25%' }} />
      )}
    </div>
  );
};

export default OptimizedImage;
