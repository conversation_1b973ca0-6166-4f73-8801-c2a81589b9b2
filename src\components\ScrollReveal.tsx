
import React, { useEffect, useRef, memo, useCallback } from 'react';

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
}

const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  className = '',
  delay = 0,
  direction = 'up'
}) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentRef = ref.current; // Store ref value to avoid stale closure

    // Optimized intersection observer with better performance settings
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Use requestAnimationFrame for better performance than setTimeout
            if (delay > 0) {
              setTimeout(() => {
                requestAnimationFrame(() => {
                  entry.target.classList.add('active');
                });
              }, delay);
            } else {
              requestAnimationFrame(() => {
                entry.target.classList.add('active');
              });
            }
            observer.unobserve(entry.target);
          }
        });
      },
      {
        root: null,
        rootMargin: '50px', // Start animation slightly before element is visible
        threshold: 0.1,
      }
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [delay]);

  // Memoize transform class calculation for better performance
  const getTransformClass = useCallback(() => {
    switch (direction) {
      case 'up':
        return 'translate-y-10';
      case 'down':
        return '-translate-y-10';
      case 'left':
        return 'translate-x-10';
      case 'right':
        return '-translate-x-10';
      default:
        return '';
    }
  }, [direction]);

  // Memoize className to prevent unnecessary re-renders
  const memoizedClassName = useCallback(() => {
    return `reveal ${getTransformClass()} ${className}`;
  }, [getTransformClass, className]);

  return (
    <div
      ref={ref}
      className={memoizedClassName()}
    >
      {children}
    </div>
  );
};

export default memo(ScrollReveal);
