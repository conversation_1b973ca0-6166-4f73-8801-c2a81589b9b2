import React, { useState, useEffect, useRef } from 'react';
import ScrollReveal from './ScrollReveal';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronLeft, ChevronRight, X } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import { preloadCriticalImages } from '@/lib/image-optimization';

// Debug: Component file is being imported
console.log('🚀 GallerySection: Component file imported successfully');

// Image data - images are in public/product-pictures/
const productImages = [
  { id: 1, src: '/product-pictures/1 (1).png', alt: 'Coffee packaging mockup by Econic Media – dramatic side lighting with shadow details' },
  { id: 2, src: '/product-pictures/1 (2).png', alt: 'Cosmetic bottle product photography by Econic Media – clean white background with soft reflections' },
  { id: 3, src: '/product-pictures/1 (3).png', alt: 'Supplement packaging visualization by Econic Media – studio lighting with product feature highlight' },
  { id: 4, src: '/product-pictures/1 (4).png', alt: 'Skincare product packaging design by Econic Media – lifestyle arrangement with botanical elements' },
  { id: 5, src: '/product-pictures/1 (5).png', alt: 'Premium perfume bottle photography by Econic Media – luxury product display with gradient background' },
  { id: 6, src: '/product-pictures/1 (6).png', alt: 'Health supplement jar design by Econic Media – professional product visualization on dark background' },
  { id: 7, src: '/product-pictures/1 (7).png', alt: 'Organic tea packaging mockup by Econic Media – natural styling with ingredient accent' },
  { id: 8, src: '/product-pictures/1 (8).png', alt: 'Wellness product photography by Econic Media – minimalist composition with brand message focus' },
  { id: 9, src: '/product-pictures/1 (9).png', alt: 'Vitamin bottle packaging design by Econic Media – e-commerce ready product visual with detail shot' },
  { id: 10, src: '/product-pictures/1 (10).png', alt: 'CBD oil product visualization by Econic Media – transparent bottle display showing liquid quality' },
  { id: 11, src: '/product-pictures/1 (11).png', alt: 'Protein powder packaging photography by Econic Media – fitness product with dynamic angle shot' },
  { id: 12, src: '/product-pictures/1 (12).png', alt: 'Beauty serum bottle design by Econic Media – premium product visualization with droplet accent' },
  { id: 13, src: '/product-pictures/1 (13).png', alt: 'Herbal supplement packaging mockup by Econic Media – natural health product visual with ingredients' },
  { id: 14, src: '/product-pictures/1 (14).png', alt: 'Facial cream jar photography by Econic Media – beauty product with texture highlight' },
  { id: 15, src: '/product-pictures/1 (15).png', alt: 'Nutrition bar packaging design by Econic Media – snack product with flavor visual emphasis' },
  { id: 16, src: '/product-pictures/1 (16).png', alt: 'Essential oil bottle visualization by Econic Media – aromatherapy product with natural backdrop' },
  { id: 17, src: '/product-pictures/1 (17).png', alt: 'Collagen supplement packaging photography by Econic Media – beauty nutrition product with elegant styling' },
  { id: 18, src: '/product-pictures/1 (18).png', alt: 'Plant-based powder container design by Econic Media – vegan product visual with ingredient scattering' },
  { id: 19, src: '/product-pictures/1 (19).png', alt: 'Hair care bottle mockup by Econic Media – salon quality product visualization with gradient background' },
  { id: 20, src: '/product-pictures/1 (20).png', alt: 'Vitamin gummy package photography by Econic Media – supplement product with playful arrangement' },
  { id: 21, src: '/product-pictures/1 (21).png', alt: 'Sports nutrition container design by Econic Media – fitness product visual with dynamic lighting' },
  { id: 22, src: '/product-pictures/1 (22).png', alt: 'Organic skincare set visualization by Econic Media – beauty product collection with botanical styling' },
  { id: 23, src: '/product-pictures/1 (23).png', alt: 'Health drink bottle mockup by Econic Media – beverage product with condensation detail' },
  { id: 24, src: '/product-pictures/1 (24).png', alt: 'Natural supplement packaging photography by Econic Media – wellness product with ingredient feature' },
];

// Define how many images to show in each row based on screen size
const imagesPerRow = {
  sm: 1,
  md: 2,
  lg: 3,
  xl: 4
};

// Calculate how many images to show initially (2 rows)
const initialImagesToShow = imagesPerRow.xl * 2; // 8 images (2 rows of 4 on desktop)

const ImageViewer = ({
  image,
  onClose,
  onNext,
  onPrev,
  currentIndex,
  totalImages
}: {
  image: { id: number; src: string; alt: string };
  onClose: () => void;
  onNext: () => void;
  onPrev: () => void;
  currentIndex: number;
  totalImages: number;
}) => {
  // Modal opened successfully

  // Add keyboard navigation for the viewer
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') {
        onNext();
      } else if (e.key === 'ArrowLeft') {
        onPrev();
      } else if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNext, onPrev, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  return (
    <div
      className="fixed inset-0 z-50 bg-black/95 backdrop-blur-sm cursor-pointer"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-label="Image viewer"
    >
      <div
        className="w-full h-full relative flex flex-col cursor-default"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button - Enhanced visibility and positioning - Top-left for mobile UX */}
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 left-4 z-50 text-white hover:text-premium-gold transition-all duration-300 p-3 min-w-[48px] min-h-[48px] rounded-full bg-black/70 hover:bg-black/90 backdrop-blur-md border-2 border-white/30 hover:border-premium-gold/70 shadow-lg hover:shadow-xl touch-manipulation active:scale-95"
          aria-label="Close image preview"
          title="Close image viewer"
        >
          <X size={24} className="drop-shadow-lg stroke-2" />
        </button>

        {/* Main image container - Centered with proper aspect ratio preservation */}
        <div className="flex-1 relative flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12">
          <motion.div
            key={image.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="relative flex items-center justify-center max-w-full max-h-full"
          >
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              width={1200}
              height={900}
              className="max-w-[90vw] max-h-[80vh] w-auto h-auto object-contain rounded-lg shadow-2xl mx-auto"
              objectFit="contain"
              priority={true}
            />
          </motion.div>

          {/* Navigation buttons - Enhanced for mobile */}
          <button
            type="button"
            className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-40 bg-black/70 hover:bg-black/90 backdrop-blur-md border-2 border-white/30 hover:border-premium-gold/70 p-2 sm:p-3 min-w-[48px] min-h-[48px] sm:min-w-[52px] sm:min-h-[52px] rounded-full text-white hover:text-premium-gold transition-all duration-300 hover:scale-110 shadow-lg touch-manipulation"
            onClick={(e) => { e.stopPropagation(); onPrev(); }}
            aria-label="Previous image"
          >
            <ChevronLeft size={20} className="sm:w-6 sm:h-6 drop-shadow-lg stroke-2" />
          </button>

          <button
            type="button"
            className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-40 bg-black/70 hover:bg-black/90 backdrop-blur-md border-2 border-white/30 hover:border-premium-gold/70 p-2 sm:p-3 min-w-[48px] min-h-[48px] sm:min-w-[52px] sm:min-h-[52px] rounded-full text-white hover:text-premium-gold transition-all duration-300 hover:scale-110 shadow-lg touch-manipulation"
            onClick={(e) => { e.stopPropagation(); onNext(); }}
            aria-label="Next image"
          >
            <ChevronRight size={20} className="sm:w-6 sm:h-6 drop-shadow-lg stroke-2" />
          </button>
        </div>

        {/* Image counter - Enhanced for mobile */}
        <div className="flex justify-center py-3 sm:py-4 relative z-10">
          <div className="bg-black/70 backdrop-blur-md border border-white/30 px-3 sm:px-4 py-2 rounded-full shadow-lg">
            <p className="text-xs sm:text-sm text-white font-medium">
              {currentIndex + 1} of {totalImages}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const GallerySection: React.FC = () => {
  console.log('🎯 GallerySection: Component function called - RENDERING STARTED');

  // Simplified state for debugging
  const [selectedImageId, setSelectedImageId] = useState<number | null>(null);
  const [showAll, setShowAll] = useState(false);
  const [hasPreloaded, setHasPreloaded] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Test if component renders at all
  return (
    <section id="gallery" className="py-20 bg-red-500">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-white text-center mb-8">
          🚨 GALLERY COMPONENT IS RENDERING! 🚨
        </h2>
        <p className="text-white text-center">
          If you see this, the component is working. Total images: {productImages.length}
        </p>
        <div className="grid grid-cols-2 gap-4 mt-8">
          {productImages.slice(0, 4).map((item) => (
            <div key={item.id} className="bg-white p-4 rounded">
              <p className="text-black">Image {item.id}: {item.src}</p>
              <img
                src={item.src}
                alt={item.alt}
                className="w-full h-32 object-cover mt-2"
                onLoad={() => console.log('✅ Image loaded:', item.src)}
                onError={() => console.error('❌ Image failed:', item.src)}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );

  // Original complex component code commented out for debugging
  /*
  // Debug logging for image loading
  useEffect(() => {
    console.log('🖼️ GallerySection: Component mounted');
    console.log('🖼️ GallerySection: Total images:', productImages.length);
    console.log('🖼️ GallerySection: First image src:', productImages[0]?.src);
    console.log('🖼️ GallerySection: Visible images count:', showAll ? productImages.length : initialImagesToShow);
  }, [showAll]);

  // Determine which images to display
  const visibleImages = showAll ? productImages : productImages.slice(0, initialImagesToShow);

  // Get the selected image object and index
  const selectedImageIndex = selectedImageId !== null
    ? productImages.findIndex(img => img.id === selectedImageId)
    : -1;

  const selectedImage = selectedImageIndex !== -1
    ? productImages[selectedImageIndex]
    : null;

  // Modal state management

  // Preload critical images when section becomes visible
  useEffect(() => {
    if (!sectionRef.current || hasPreloaded) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasPreloaded) {
            // Preload only the first 4 images that will be visible above the fold
            const criticalImages = productImages.slice(0, 4).map(img => img.src);

            preloadCriticalImages(criticalImages).then(() => {
              setHasPreloaded(true);
            }).catch((error) => {
              console.warn('Failed to preload gallery images:', error);
            });
          }
        });
      },
      {
        rootMargin: '100px', // Start preloading 100px before the section is visible
        threshold: 0.1
      }
    );

    observer.observe(sectionRef.current);

    return () => {
      observer.disconnect();
    };
  }, [hasPreloaded]);

  // Navigate to next or previous image
  const navigateImage = (direction: 'next' | 'prev') => {
    if (selectedImageIndex === -1) return;

    let newIndex: number;

    if (direction === 'next') {
      newIndex = (selectedImageIndex + 1) % productImages.length;
    } else {
      newIndex = (selectedImageIndex - 1 + productImages.length) % productImages.length;
    }

    setSelectedImageId(productImages[newIndex].id);
  };

  return (
    <section ref={sectionRef} id="gallery" className="section-padding relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Enhanced gradient orb decorations - Static - Optimized for mobile */}
      <div className="absolute top-20 right-1/4 w-40 h-40 sm:w-48 sm:h-48 md:w-64 md:h-64 bg-gradient-ocean opacity-20 sm:opacity-30 rounded-full blur-2xl sm:blur-3xl"></div>
      <div className="absolute bottom-20 left-1/4 w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80 bg-gradient-sunset opacity-20 sm:opacity-25 rounded-full blur-2xl sm:blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 w-60 h-60 sm:w-80 sm:h-80 md:w-96 md:h-96 bg-gradient-aurora opacity-5 sm:opacity-10 rounded-full blur-2xl sm:blur-3xl"></div>

      {/* Premium floating particles - Static */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-premium-gold rounded-full opacity-60"></div>
      <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-premium-silver rounded-full opacity-40"></div>
      <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-premium-platinum rounded-full opacity-50"></div>



      <div className="section-container">
        <div className="section-header">
          <ScrollReveal>
            <div className="inline-block mb-3 sm:mb-4 gradient-flow-tag gradient-flow-gallery text-xs sm:text-sm font-medium">
              <span className="text-gradient-lightning-white">✨ Visual Portfolio</span>
            </div>
          </ScrollReveal>

          <ScrollReveal delay={200}>
            <h2 className="section-title-enhanced text-gradient-lightning-white drop-shadow-glow">
              Our Visual Craftsmanship
            </h2>
          </ScrollReveal>

          <ScrollReveal delay={400}>
            <p className="section-description">
              Explore our product photography and website designs created to elevate brand identity and boost conversions.
            </p>
          </ScrollReveal>

          {/* Debug info - temporary */}
          <div className="bg-red-500/20 border border-red-500 p-4 rounded-lg mb-4 text-white">
            <h3 className="font-bold">🔍 Debug Info:</h3>
            <p>Total images: {productImages.length}</p>
            <p>Visible images: {visibleImages.length}</p>
            <p>First image: {productImages[0]?.src}</p>
            <p>Show all: {showAll ? 'Yes' : 'No'}</p>
          </div>
        </div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
          layout
          transition={{
            duration: 0.6,
            type: "spring",
            stiffness: 100,
            damping: 15
          }}
          id="gallery-grid"
        >
          <AnimatePresence>
            {visibleImages.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{
                  duration: 0.4,
                  delay: Math.min(index * 0.05, 0.5),
                  ease: "easeOut"
                }}
              >
                <div className="group relative overflow-hidden rounded-xl sm:rounded-2xl luxury-card hover:shadow-neon-glow transition-all duration-500 h-full flex flex-col hover:translate-y-[-4px] hover:border-neon-cyan touch-manipulation">
                  {/* Enhanced premium glow effects - Optimized for mobile */}
                  <div className="absolute -inset-1 sm:-inset-2 rounded-2xl sm:rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-30 sm:group-hover:opacity-40 blur-lg sm:blur-xl z-0 transition-opacity duration-500"></div>
                  <div className="absolute -inset-0.5 sm:-inset-1 rounded-xl sm:rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-20 blur-md sm:blur-lg z-0 transition-opacity duration-500"></div>

                  {/* Premium border frame */}
                  <div className="absolute -inset-0.5 rounded-2xl bg-gradient-premium opacity-0 group-hover:opacity-60 z-0 transition-opacity duration-500"></div>

                  <div
                    className="relative w-full aspect-[4/3] overflow-hidden cursor-pointer z-10"
                    onClick={() => setSelectedImageId(item.id)}
                  >
                    <OptimizedImage
                      src={item.src}
                      alt={item.alt}
                      width={800}
                      height={600}
                      className="w-full h-full transition-transform duration-700 group-hover:scale-105"
                      objectFit="cover"
                      loading="lazy"
                    />
                    {/* Enhanced gradient overlay for image */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 group-hover:from-black/20 transition-all duration-500"></div>
                  </div>

                  {/* Premium accent elements - Static */}
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-premium-gold rounded-full opacity-0 group-hover:opacity-80 z-20 transition-opacity duration-500"></div>
                  <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-premium-silver rounded-full opacity-0 group-hover:opacity-60 z-20 transition-opacity duration-500"></div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {productImages.length > initialImagesToShow && (
          <motion.div
            className="flex justify-center mt-8 sm:mt-10 md:mt-16"
            layout
            transition={{ duration: 0.4 }}
          >
            <button
              type="button"
              onClick={() => setShowAll(!showAll)}
              className="flex items-center justify-center gap-2 sm:gap-3 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4 min-h-[48px] min-w-[120px] premium-glass hover:border-premium-gold/30 hover:shadow-[0_8px_30px_rgba(255,215,0,0.3)] text-white rounded-xl font-semibold transition-all duration-500 group hover:scale-105 active:scale-95 touch-manipulation"
            >
              <span className="text-gradient-lightning-white">{showAll ? 'Show Less' : 'Show More'}</span>
              <motion.div
                animate={{ rotate: showAll ? 180 : 0 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <ChevronDown className="w-5 h-5 text-premium-gold group-hover:text-neon-cyan transition-colors duration-300" />
              </motion.div>
            </button>
          </motion.div>
        )}
      </div>

      {/* Image Viewer Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <ImageViewer
              image={selectedImage}
              onClose={() => setSelectedImageId(null)}
              onNext={() => navigateImage('next')}
              onPrev={() => navigateImage('prev')}
              currentIndex={selectedImageIndex}
              totalImages={productImages.length}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default GallerySection;
