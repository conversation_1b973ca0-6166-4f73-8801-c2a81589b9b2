{"framework": "vite", "buildCommand": "npm run build", "installCommand": "npm install", "outputDirectory": "dist", "rewrites": [{"source": "/:path((?!.*\\.).*)", "destination": "/index.html"}], "cleanUrls": true, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.css$", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.(js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}